import 'dart:io';
import 'package:eva_icons_flutter/eva_icons_flutter.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:wicker/screens/add_product_screen.dart';
import 'package:wicker/screens/sales_history_screen.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/services/inventory_service.dart';
import 'package:wicker/widgets/editable_product_card.dart';
import 'package:wicker/widgets/neu_expandable_fab.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';

// --- NEW: The missing enum definition ---
enum UpdateAction { add, replace, cancel }
// --- End of NEW ---

class InventoryManagementScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const InventoryManagementScreen({super.key, required this.businessData});

  @override
  State<InventoryManagementScreen> createState() =>
      _InventoryManagementScreenState();
}

class _InventoryManagementScreenState extends State<InventoryManagementScreen> {
  final EcommerceService _ecommerceService = EcommerceService();
  final InventoryService _inventoryService = InventoryService();
  final ImagePicker _picker = ImagePicker();

  late Future<List<Map<String, dynamic>>> _productsFuture;
  Future<List<dynamic>>? _alertsFuture;
  bool _isScanning = false;

  @override
  void initState() {
    super.initState();
    _fetchData();
  }

  void _fetchData() {
    _fetchProducts();
    _fetchAlerts();
  }

  void _fetchProducts() {
    final businessId = widget.businessData['_id']['\$oid'];
    setState(() {
      _productsFuture = _ecommerceService.getBusinessProducts(businessId);
    });
  }

  void _fetchAlerts() {
    setState(() {
      _alertsFuture = _inventoryService.getRestockAlerts();
    });
  }

  Future<void> _scanShelf() async {
    final XFile? imageFile = await _picker.pickImage(
      source: ImageSource.camera,
      imageQuality: 70,
    );
    if (imageFile == null) return;

    setState(() => _isScanning = true);

    try {
      final results = await _inventoryService.analyzeShelfImage(
        File(imageFile.path),
      );
      if (mounted) {
        await _showShelfScanResults(results['inventory'] as List<dynamic>);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('AI Scan Failed: ${e.toString()}')),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isScanning = false);
      }
    }
  }


  

  // Future<void> _showShelfScanResults(List<dynamic> inventory) async {
  //   if (inventory.isEmpty) {
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(
  //         content: Text('The AI did not find any of your products.'),
  //       ),
  //     );
  //     return;
  //   }

  //   final UpdateAction? chosenAction = await showDialog<UpdateAction>(
  //     context: context,
  //     builder: (context) => AlertDialog(
  //       title: const Text('AI Scan Results'),
  //       content: SizedBox(
  //         width: double.maxFinite,
  //         child: ListView.builder(
  //           shrinkWrap: true,
  //           itemCount: inventory.length,
  //           itemBuilder: (context, index) {
  //             final item = inventory[index];
  //             return ListTile(
  //               title: Text(item['product_name']),
  //               trailing: Text('Found: ${item['quantity']}'),
  //             );
  //           },
  //         ),
  //       ),
  //       actions: [
  //         TextButton(
  //           onPressed: () => Navigator.of(context).pop(UpdateAction.cancel),
  //           child: const Text('Cancel'),
  //         ),
  //         ElevatedButton(
  //           onPressed: () => Navigator.of(context).pop(UpdateAction.replace),
  //           child: const Text('Replace Count'),
  //         ),
  //         ElevatedButton(
  //           onPressed: () => Navigator.of(context).pop(UpdateAction.add),
  //           style: ElevatedButton.styleFrom(
  //             backgroundColor: Theme.of(context).primaryColor,
  //           ),
  //           child: const Text('Add to Stock'),
  //         ),
  //       ],
  //     ),
  //   );

  //   if (chosenAction == UpdateAction.add ||
  //       chosenAction == UpdateAction.replace) {
  //     final products = await _productsFuture;
  //     for (var foundItem in inventory) {
  //       final product = products.firstWhere(
  //         (p) => p['product_name'] == foundItem['product_name'],
  //         orElse: () => {},
  //       );
  //       if (product.isNotEmpty) {
  //         final productId = product['_id']['\$oid'];
  //         final quantity = foundItem['quantity'] as int;

  //         if (chosenAction == UpdateAction.add) {
  //           await _ecommerceService.addStock(productId, quantity);
  //         } else {
  //           await _ecommerceService.updateProductStock(productId, quantity);
  //         }
  //       }
  //     }
  //     _fetchProducts(); // Refresh the list to show new totals
  //     ScaffoldMessenger.of(context).showSnackBar(
  //       const SnackBar(content: Text('Inventory updated successfully!')),
  //     );
  //   }
  // }




Future<void> _showShelfScanResults(List<dynamic> inventory) async {
  if (inventory.isEmpty) {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('The AI did not find any of your products.'),
      ),
    );
    return;
  }

  final UpdateAction? chosenAction = await showDialog<UpdateAction>(
    context: context,
    barrierDismissible: false,
    builder: (context) => Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        decoration: BoxDecoration(
          color: const Color(0xFFFEF7F0),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.black, width: 3),
          boxShadow: const [
            BoxShadow(
              color: Colors.black,
              offset: Offset(4, 4),
              blurRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: const BoxDecoration(
                color: Color(0xFFFFE66D),
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(17),
                  topRight: Radius.circular(17),
                ),
                border: Border(
                  bottom: BorderSide(color: Colors.black, width: 3),
                ),
              ),
              child: Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.black, width: 2),
                    ),
                    child: const Icon(
                      EvaIcons.checkmarkCircle2,
                      color: Colors.black,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    'AI Scan Complete',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: Colors.black,
                    ),
                  ),
                ],
              ),
            ),
            
            // Product List
            Container(
              constraints: const BoxConstraints(maxHeight: 250),
              child: ListView.builder(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                itemCount: inventory.length,
                itemBuilder: (context, index) {
                  final item = inventory[index];
                  return Container(
                    margin: const EdgeInsets.only(bottom: 8),
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.black, width: 2),
                      boxShadow: const [
                        BoxShadow(
                          color: Colors.black,
                          offset: Offset(2, 2),
                          blurRadius: 0,
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            item['product_name'],
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 14,
                            ),
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: const Color(0xFF4ECDC4),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(color: Colors.black, width: 1.5),
                          ),
                          child: Text(
                            '${item['quantity']}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            
            // Action Buttons
            Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  const Text(
                    'How should I update your inventory?',
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      // Replace Button
                      Expanded(
                        child: _ActionButton(
                          onPressed: () => Navigator.of(context).pop(UpdateAction.replace),
                          backgroundColor: const Color(0xFFFF6B6B),
                          icon: Icons.refresh_rounded,
                          label: 'REPLACE',
                          subtitle: 'Set to count',
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Add Button
                      Expanded(
                        child: _ActionButton(
                          onPressed: () => Navigator.of(context).pop(UpdateAction.add),
                          backgroundColor: const Color(0xFF4ECDC4),
                          icon: Icons.add_circle_outline_rounded,
                          label: 'ADD',
                          subtitle: 'Add to existing',
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Cancel Button
                  SizedBox(
                    width: double.infinity,
                    child: TextButton(
                      onPressed: () => Navigator.of(context).pop(UpdateAction.cancel),
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                      ),
                      child: const Text(
                        'Cancel',
                        style: TextStyle(
                          color: Colors.black54,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );

  if (chosenAction == UpdateAction.add || chosenAction == UpdateAction.replace) {
    // Show loading indicator
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(
        child: CircularProgressIndicator(),
      ),
    );

    try {
      final products = await _productsFuture;
      for (var foundItem in inventory) {
        final product = products.firstWhere(
          (p) => p['product_name'] == foundItem['product_name'],
          orElse: () => {},
        );
        if (product.isNotEmpty) {
          final productId = product['_id']['\$oid'];
          final quantity = foundItem['quantity'] as int;

          if (chosenAction == UpdateAction.add) {
            await _ecommerceService.addStock(productId, quantity);
          } else {
            await _ecommerceService.updateProductStock(productId, quantity);
          }
        }
      }
      
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();
      
      _fetchProducts(); // Refresh the list
      
      // Show success animation
      if (mounted) {
        _showSuccessAnimation(chosenAction);
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Update failed: ${e.toString()}')),
        );
      }
    }
  }
}

// Success Animation Method
void _showSuccessAnimation(UpdateAction action) {
  showDialog(
    context: context,
    barrierDismissible: true,
    builder: (context) => Dialog(
      backgroundColor: Colors.transparent,
      elevation: 0,
      child: Container(
        padding: const EdgeInsets.all(32),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.black, width: 3),
          boxShadow: const [
            BoxShadow(
              color: Colors.black,
              offset: Offset(4, 4),
              blurRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TweenAnimationBuilder<double>(
              duration: const Duration(milliseconds: 600),
              tween: Tween(begin: 0, end: 1),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: value,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: action == UpdateAction.add 
                        ? const Color(0xFF4ECDC4)
                        : const Color(0xFFFF6B6B),
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.black, width: 3),
                    ),
                    child: Icon(
                      action == UpdateAction.add 
                        ? Icons.add_task_rounded
                        : Icons.published_with_changes_rounded,
                      size: 40,
                      color: Colors.white,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 20),
            Text(
              action == UpdateAction.add 
                ? 'Stock Added!' 
                : 'Stock Replaced!',
              style: const TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Inventory updated successfully',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    ),
  );
  
  // Auto-close after 2 seconds
  Future.delayed(const Duration(seconds: 2), () {
    if (mounted) Navigator.of(context).pop();
  });
}

// Custom Action Button Widget
class _ActionButton extends StatefulWidget {
  final VoidCallback onPressed;
  final Color backgroundColor;
  final IconData icon;
  final String label;
  final String subtitle;

  const _ActionButton({
    required this.onPressed,
    required this.backgroundColor,
    required this.icon,
    required this.label,
    required this.subtitle,
  });

  @override
  State<_ActionButton> createState() => _ActionButtonState();
}

class _ActionButtonState extends State<_ActionButton> 
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 150),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        setState(() => _isPressed = true);
        _controller.forward();
      },
      onTapUp: (_) {
        setState(() => _isPressed = false);
        _controller.reverse();
        widget.onPressed();
      },
      onTapCancel: () {
        setState(() => _isPressed = false);
        _controller.reverse();
      },
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 150),
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.black, width: 2.5),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black,
                    offset: _isPressed ? const Offset(1, 1) : const Offset(3, 3),
                    blurRadius: 0,
                  ),
                ],
              ),
              child: Column(
                children: [
                  Icon(
                    widget.icon,
                    color: Colors.white,
                    size: 28,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    widget.label,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    widget.subtitle,
                    style: const TextStyle(
                      color: Colors.white70,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }
}






  void _navigateToAddProduct() async {
    final businessId = widget.businessData['_id']['\$oid'];
    final result = await Navigator.push<bool>(
      context,
      MaterialPageRoute(
        builder: (context) => AddProductScreen(businessId: businessId),
      ),
    );
    if (result == true) {
      _fetchData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back, color: Colors.black),
            onPressed: () => Navigator.of(context).pop(),
          ),
          title: Text(
            widget.businessData['business_name'] ?? 'My Inventory',
            style: const TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(EvaIcons.barChart2Outline, color: Colors.black),
              tooltip: 'Sales History',
              onPressed: () => Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const SalesHistoryScreen(),
                ),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(4.0),
            child: Container(color: Colors.black, height: 3.0),
          ),
        ),
      ),
      body: Column(
        children: [
          _buildAlertsSection(),
          Expanded(
            child: FutureBuilder<List<Map<String, dynamic>>>(
              future: _productsFuture,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('Error: ${snapshot.error}'));
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return Center(
                    child: NeuCard(
                      backgroundColor: const Color(0xFFFFE66D),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.inventory_2_outlined,
                              size: 48,
                              color: Colors.grey[800],
                            ),
                            const SizedBox(height: 16),
                            const Text(
                              'NO PRODUCTS YET',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Tap the action button to add your first product.',
                              style: TextStyle(color: Colors.grey[800]),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }

                final products = snapshot.data!;
                return RefreshIndicator(
                  onRefresh: () async => _fetchProducts(),
                  child: ListView.builder(
                    padding: const EdgeInsets.fromLTRB(0, 8, 0, 80),
                    itemCount: products.length,
                    itemBuilder: (context, index) {
                      return EditableProductCard(
                        productData: products[index],
                        onStockUpdated: _fetchData,
                      );
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: NeuExpandableFab(
        distance: 120.0,
        actions: [
          FabAction(onPressed: _navigateToAddProduct, icon: Icons.add),
          FabAction(
            onPressed: _isScanning ? () {} : _scanShelf,
            icon: _isScanning ? Icons.hourglass_top_rounded : EvaIcons.camera,
          ),
        ],
      ),
    );
  }

  Widget _buildAlertsSection() {
    return FutureBuilder<List<dynamic>>(
      future: _alertsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting ||
            !snapshot.hasData ||
            snapshot.data!.isEmpty) {
          return const SizedBox.shrink();
        }
        final alerts = snapshot.data!;
        return NeuCard(
          margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
          backgroundColor: const Color(0xFFFF6B6B),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  const Icon(EvaIcons.alertTriangle, color: Colors.white),
                  const SizedBox(width: 8),
                  const Text(
                    "Restock Alerts",
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              ...alerts
                  .map(
                    (alert) => Text(
                      "• ${alert['product_name']}: Only ${alert['current_stock']} left (~${alert['days_remaining']} days).",
                      style: const TextStyle(color: Colors.white),
                    ),
                  )
                  .toList(),
            ],
          ),
        );
      },
    );
  }
}
