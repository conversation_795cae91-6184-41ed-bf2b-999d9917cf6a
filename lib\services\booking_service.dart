import 'dart:convert';
import 'package:intl/intl.dart'; // Import the intl package for date formatting
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/places_service.dart'; // For WickerHttpClient

class BookingService {
  final WickerHttpClient _client = WickerHttpClient();
  final ConfigService _config = ConfigService.instance;

  Future<void> setAvailability({
    required String businessId,
    required Map<String, dynamic> schedule,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/availability/$businessId'),
      body: jsonEncode({'availability': schedule}),
    );

    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to set availability');
    }
  }

  // --- NEW: Method to fetch available time slots ---
  Future<List<String>> getAvailability({
    required String businessId,
    required DateTime date,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final dateString = DateFormat('yyyy-MM-dd').format(date);
    final uri = Uri.parse(
      '$baseUrl/api/bookings/availability/$businessId',
    ).replace(queryParameters: {'date': dateString});

    final response = await _client.get(uri);

    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<String>.from(data);
    } else {
      throw Exception('Failed to load availability');
    }
  }

  // --- NEW: Method to send a booking request ---
  Future<void> requestBooking({
    required String businessId,
    required String serviceId,
    required DateTime startTime,
  }) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.post(
      Uri.parse('$baseUrl/api/bookings/request'),
      body: jsonEncode({
        'business_id': businessId,
        'service_id': serviceId,
        'start_time': startTime.toIso8601String(),
      }),
    );

    if (response.statusCode != 201) {
      final body = jsonDecode(response.body);
      throw Exception(body['msg'] ?? 'Failed to send booking request');
    }
  }
}
