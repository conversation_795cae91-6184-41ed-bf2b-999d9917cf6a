import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:image_picker/image_picker.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/auth_service.dart';
import 'package:wicker/services/places_service.dart';

class EcommerceService {
  final ConfigService _config = ConfigService.instance;
  final AuthService _auth = AuthService();
  final WickerHttpClient _client = WickerHttpClient();

  // --- NEW: Adds a product to the user's business ---
  Future<void> addProduct({
    required String businessId,
    required String productName,
    required String description,
    required double price,
    required String productType,
    String? productSubType, // NEW: Added productSubType
    String? barcode,
    int stockCount = 1,
    required List<XFile> media,
  }) async {
    print('=== EcommerceService.addProduct ===');
    print('Business ID: $businessId');
    print('Product Name: $productName');
    print('Description: $description');
    print('Price: $price');
    print('Product Type: $productType');
    print('Product Sub Type: $productSubType');
    print('Barcode: $barcode');
    print('Stock Count: $stockCount');
    print('Media files count: ${media.length}');

    final baseUrl = await _config.getBaseUrl();
    print('Base URL: $baseUrl');

    final token = await _auth.getAccessToken();
    print('Token length: ${token?.length ?? 0}');

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/ecommerce/product/add'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    print('Request URI: ${request.url}');

    // Add fields
    request.fields['business_id'] = businessId;
    request.fields['product_name'] = productName;
    request.fields['description'] = description;
    request.fields['price'] = price.toString();
    request.fields['product_type'] = productType;
    if (productSubType != null) {
      // NEW: Add sub-type to the request if it exists
      request.fields['product_sub_type'] = productSubType;
    }
    if (barcode != null) {
      request.fields['barcode'] = barcode;
    }
    request.fields['stock_count'] = stockCount.toString();

    print('Request fields: ${request.fields}');

    // Add media files
    for (int i = 0; i < media.length; i++) {
      final file = media[i];
      print('Adding media file $i: ${file.path}');
      request.files.add(await http.MultipartFile.fromPath('media', file.path));
    }
    print('Total files added: ${request.files.length}');

    print('Sending request...');
    final response = await request.send();
    print('Response status code: ${response.statusCode}');

    final responseBody = await response.stream.bytesToString();
    print('Response body: $responseBody');

    if (response.statusCode != 201) {
      print('Request failed with status ${response.statusCode}');
      throw Exception('Failed to add product: $responseBody');
    }
    print('Product added successfully');
  }

  Future<Map<String, dynamic>?> getMyBusiness() async {
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    final response = await http.get(
      Uri.parse('$baseUrl/api/ecommerce/business/my-business'),
      headers: {'Authorization': 'Bearer $token'},
    );

    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    if (response.statusCode == 404) {
      return null;
    }
    throw Exception('Failed to fetch business data');
  }

  Future<Map<String, dynamic>> getBusinessDetails(String businessId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/ecommerce/business/$businessId'),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    throw Exception('Failed to load business details');
  }

  // --- NEW: Gets a business by the owner's user ID ---
  Future<Map<String, dynamic>?> getBusinessByOwner(String ownerId) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/ecommerce/business/owner/$ownerId'),
    );
    if (response.statusCode == 200) {
      return jsonDecode(response.body);
    }
    if (response.statusCode == 404) {
      return null; // It's not an error if the user simply doesn't have a business
    }
    throw Exception('Failed to fetch business data');
  }

  // --- NEW: Gets all products for a given business ID ---
  Future<List<Map<String, dynamic>>> getBusinessProducts(
    String businessId,
  ) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.get(
      Uri.parse('$baseUrl/api/ecommerce/business/$businessId/products'),
    );
    if (response.statusCode == 200) {
      final List<dynamic> data = jsonDecode(response.body);
      return List<Map<String, dynamic>>.from(data);
    }
    throw Exception('Failed to load products');
  }

  // --- THE FIX: Removed the unused positional 'text' parameter ---
  Future<void> createBusiness({
    required String businessName,
    required String description,
    required List<XFile> images,
  }) async {
    // --- End of FIX ---
    final baseUrl = await _config.getBaseUrl();
    final token = await _auth.getAccessToken();

    var request = http.MultipartRequest(
      'POST',
      Uri.parse('$baseUrl/api/ecommerce/business/create'),
    );
    request.headers['Authorization'] = 'Bearer $token';
    request.fields['business_name'] = businessName;
    request.fields['description'] = description;

    for (var image in images) {
      request.files.add(
        await http.MultipartFile.fromPath('images', image.path),
      );
    }

    final response = await request.send();
    if (response.statusCode != 201) {
      throw Exception('Failed to create business');
    }
  }

  Future<void> updateProductStock(String productId, int newQuantity) async {
    final baseUrl = await _config.getBaseUrl();
    final response = await _client.put(
      Uri.parse('$baseUrl/api/ecommerce/product/$productId/update-stock'),
      body: jsonEncode({'quantity': newQuantity}),
    );
    if (response.statusCode != 200) {
      throw Exception('Failed to update stock');
    }
  }

  Future<void> addStock(String productId, int quantityToAdd) async {
    print('=== EcommerceService.addStock ===');
    print('Product ID: $productId');
    print('Quantity to add: $quantityToAdd');

    final baseUrl = await _config.getBaseUrl();
    print('Base URL: $baseUrl');

    final uri = Uri.parse(
      '$baseUrl/api/ecommerce/product/$productId/add-stock',
    );
    print('Request URI: $uri');

    final requestBody = jsonEncode({'quantity': quantityToAdd});
    print('Request body: $requestBody');

    final response = await _client.put(uri, body: requestBody);
    print('Response status code: ${response.statusCode}');
    print('Response body: ${response.body}');

    if (response.statusCode != 200) {
      final body = jsonDecode(response.body);
      print('Error response: $body');
      throw Exception(body['msg'] ?? 'Failed to add stock');
    }
    print('Stock added successfully');
  }
}
