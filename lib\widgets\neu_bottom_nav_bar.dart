import 'package:flutter/material.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:provider/provider.dart';
import 'package:wicker/services/notification_service.dart';
import 'package:wicker/widgets/glowing_border_wrapper.dart';

/// A custom Neubrutalism-styled bottom navigation bar.
class NeuBottomNavBar extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTabTapped;
  final List<NeuNavBarItem> items;

  const NeuBottomNavBar({
    super.key,
    required this.currentIndex,
    required this.onTabTapped,
    required this.items,
  });

  @override
  Widget build(BuildContext context) {
    // Use a Consumer to get the latest notification state
    return Consumer<NotificationService>(
      builder: (context, notificationService, child) {
        return NeuCard(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: List.generate(items.length, (index) {
              final item = items[index];
              bool isSelected = index == currentIndex;

              // --- The Glow Logic ---
              bool shouldGlow =
                  (item.label == 'Hub' && notificationService.shouldHubGlow);

              Widget navItem = NeuCard(
                margin: const EdgeInsets.symmetric(horizontal: 4),
                padding: const EdgeInsets.symmetric(vertical: 8),
                backgroundColor: isSelected
                    ? const Color(0xFFFFE66D)
                    : Colors.white,
                shadowOffset: 4,
                borderWidth: 2,
                borderRadius: 12,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isSelected ? item.activeIcon : item.icon,
                      color: isSelected ? Colors.black : Colors.grey.shade700,
                      size: item.iconSize,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.label,
                      style: TextStyle(
                        color: isSelected ? Colors.black : Colors.grey.shade700,
                        fontWeight: isSelected
                            ? FontWeight.bold
                            : FontWeight.normal,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              );

              return Expanded(
                child: GestureDetector(
                  onTap: () => onTabTapped(index),
                  child: GlowingBorderWrapper(
                    showGlow: shouldGlow,
                    child: navItem,
                  ),
                ),
              );
            }),
          ),
        );
      },
    );
  }
}

/// Represents a single item in the [NeuBottomNavBar].
class NeuNavBarItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final double iconSize;

  NeuNavBarItem({
    required this.icon,
    required this.label,
    IconData? activeIcon,
    this.iconSize = 24.0,
  }) : activeIcon = activeIcon ?? icon;
}
