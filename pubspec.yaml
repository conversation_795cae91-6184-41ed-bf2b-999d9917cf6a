name: wicker
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  eva_icons_flutter: ^3.1.0
  http: ^1.5.0-beta.2 # Updated to latest stable
  flutter_secure_storage: ^9.2.4 # Updated to latest
  video_player: ^2.10.0 # Updated to latest
  just_audio: ^0.10.4   # A feature-rich audio player for voice notes
  flutter_staggered_grid_view: ^0.7.0
  flutter_map: ^8.2.1 # Updated to latest
  latlong2: ^0.9.1 # Updated to latest
  geolocator: ^10.0.0
  flutter_rating_bar: ^4.0.1
  image_picker: ^1.1.2
  neubrutalism_ui: ^2.0.0
  shared_preferences: ^2.3.3
  permission_handler: ^11.3.1
  video_thumbnail: ^0.5.3
  path_provider: ^2.1.4
  mime: ^1.0.6
  jwt_decoder: ^2.0.1
  share_plus: ^7.0.0
  phosphor_flutter: ^2.1.0
  mobile_scanner: ^7.0.1
  webview_flutter: ^4.7.0 # Or the latest version
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  lottie: ^3.1.2


  
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  universal_html: ^2.2.4
  cached_network_image: ^3.4.1
  timeago: ^3.7.1


dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0

# Force update transitive dependencies to their latest versions
dependency_overrides:
  characters: ^1.4.1
  material_color_utilities: ^0.13.0
  meta: ^1.17.0
  vector_math: ^2.2.0
  test_api: ^0.7.6
  vm_service: ^15.0.2
  leak_tracker: ^11.0.1
  leak_tracker_flutter_testing: ^3.0.10
  leak_tracker_testing: ^3.0.2
  # Flutter secure storage platform implementations
  flutter_secure_storage_linux: ^2.0.1
  flutter_secure_storage_macos: ^4.0.0
  flutter_secure_storage_platform_interface: ^2.0.1
  flutter_secure_storage_web: ^2.0.0
  flutter_secure_storage_windows: ^4.0.0
  # Other outdated packages
  unicode: ^1.1.8

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  


  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
