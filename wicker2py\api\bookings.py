from flask import request, jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId
import datetime

bookings_bp = Blueprint('bookings_bp', __name__)

# In flask_api/api/bookings.py

@bookings_bp.route('/availability/<business_id>', methods=['POST'])
@jwt_required()
def set_availability(business_id):
    """
    VENDOR-ONLY endpoint. Sets the weekly work schedule for a specific business.
    """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    # --- REFACTORED: Find the specific business and verify ownership ---
    business = db.businesses.find_one({
        "_id": ObjectId(business_id),
        "owner_id": current_user_id
    })
    if not business:
        return jsonify({"msg": "Business not found or you do not have permission."}), 404
    # --- End of REFACTOR ---

    availability_schedule = data.get('availability')
    if not isinstance(availability_schedule, dict):
        return jsonify({"msg": "Invalid availability format."}), 400

    try:
        db.businesses.update_one(
            {"_id": ObjectId(business_id)},
            {"$set": {"availability": availability_schedule}}
        )
        return jsonify({"msg": "Availability updated successfully."}), 200
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
@bookings_bp.route('/availability/<business_id>', methods=['GET'])
@jwt_required()
def get_availability(business_id):
    """
    CLIENT-FACING endpoint. Gets available time slots for a business on a specific date.
    """
    db = bookings_bp.db
    date_str = request.args.get('date') # Expects 'YYYY-MM-DD'
    if not date_str:
        return jsonify({"msg": "A 'date' query parameter is required."}), 400

    try:
        requested_date = datetime.datetime.strptime(date_str, '%Y-%m-%d').date()
        day_of_week = requested_date.strftime('%A').lower()

        business = db.businesses.find_one({"_id": ObjectId(business_id)})
        if not business or 'availability' not in business:
            return jsonify([]), 200 # Return empty list if no schedule is set

        day_schedule = business['availability'].get(day_of_week)
        if not day_schedule:
            return jsonify([]), 200 # Vendor does not work on this day

        # Find all confirmed bookings for this business on the requested day
        start_of_day = datetime.datetime.combine(requested_date, datetime.time.min)
        end_of_day = datetime.datetime.combine(requested_date, datetime.time.max)
        
        booked_slots = list(db.bookings.find({
            "business_id": ObjectId(business_id),
            "status": "confirmed",
            "start_time": {"$gte": start_of_day, "$lt": end_of_day}
        }))
        
        booked_start_times = {slot['start_time'].time() for slot in booked_slots}

        # Generate all possible slots and filter out the booked ones
        available_slots = []
        slot_duration = datetime.timedelta(hours=1) # Assuming 1-hour slots for now
        
        current_time = datetime.datetime.strptime(day_schedule['start'], '%H:%M')
        end_time = datetime.datetime.strptime(day_schedule['end'], '%H:%M')

        while current_time < end_time:
            if current_time.time() not in booked_start_times:
                available_slots.append(current_time.strftime('%H:%M'))
            current_time += slot_duration
        
        return jsonify(available_slots), 200

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    



@bookings_bp.route('/request', methods=['POST'])
@jwt_required()
def request_booking():
    """
    CLIENT-ONLY endpoint. Creates a new booking request for a service.
    """
    db = bookings_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()

    business_id = data.get('business_id')
    service_id = data.get('service_id')
    start_time_str = data.get('start_time')

    if not all([business_id, service_id, start_time_str]):
        return jsonify({"msg": "business_id, service_id, and start_time are required."}), 400

    try:
        start_time = datetime.datetime.fromisoformat(start_time_str)
        
        # --- REFACTORED: Fetch the service to get its actual duration ---
        service = db.products.find_one({"_id": ObjectId(service_id)})
        if not service:
            return jsonify({"msg": "Service (product) not found"}), 404
        
        duration = service.get('duration_minutes', 60) # Default to 60 mins if not set
        end_time = start_time + datetime.timedelta(minutes=duration)
        # --- End of REFACTOR ---

        business = db.businesses.find_one({"_id": ObjectId(business_id)})
        if not business:
            return jsonify({"msg": "Business not found"}), 404

        new_booking = {
            "client_id": current_user_id,
            "seller_id": business['owner_id'],
            "business_id": ObjectId(business_id),
            "service_id": ObjectId(service_id),
            "start_time": start_time,
            "end_time": end_time, # Use the accurately calculated end_time
            "status": "pending_confirmation",
            "created_at": datetime.datetime.now(datetime.timezone.utc)
        }
        
        db.bookings.insert_one(new_booking)

        return jsonify({"msg": "Booking request sent successfully. Awaiting vendor confirmation."}), 201

    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500