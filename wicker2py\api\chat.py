import json
from flask import jsonify, Blueprint
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime
from app import socketio # Import the socketio instance from our main app
from flask_socketio import emit, join_room, leave_room

chat_bp = Blueprint('chat_bp', __name__)

# This is a standard HTTP endpoint to fetch the message history for a room
@chat_bp.route('/history/<room_id>', methods=['GET'])
@jwt_required()
def get_history(room_id):
    """Fetches the chat history for a given room (e.g., an order ID)."""
    db = chat_bp.db
    
    # Optional: Add logic here to verify the current user is part of this chat room
    
    messages = list(db.messages.find({'room_id': room_id}).sort('timestamp', 1))
    return json.loads(json_util.dumps(messages)), 200

# --- Real-Time WebSocket Event Handlers ---

@socketio.on('join')
def on_join(data):
    """User joins a room."""
    room = data['room']
    join_room(room)
    print(f"User has entered room: {room}")

@socketio.on('leave')
def on_leave(data):
    """User leaves a room."""
    room = data['room']
    leave_room(room)
    print(f"User has left room: {room}")

@socketio.on('send_message')
def on_send_message(data):
    """Receives a message from a client and broadcasts it to the room."""
    db = chat_bp.db
    room = data.get('room')
    if not room:
        return # Ignore messages without a room

    # In the next phase, we will intercept this message and send it to the AI Chaperone
    
    message_doc = {
        "room_id": room,
        "author_id": ObjectId(data.get('author_id')),
        "author_username": data.get('author_username'),
        "text": data.get('text'),
        "timestamp": datetime.datetime.now(datetime.timezone.utc)
    }
    
    # Save the message to the database for history
    db.messages.insert_one(message_doc)
    
    # Broadcast the message to all clients in the room
    emit('receive_message', json.loads(json_util.dumps(message_doc)), to=room)