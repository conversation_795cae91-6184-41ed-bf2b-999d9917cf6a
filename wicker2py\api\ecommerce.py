# import json
# from flask import Response, logging, request, jsonify, Blueprint, request
# from werkzeug.utils import secure_filename
# import os
# import uuid
# from flask_jwt_extended import jwt_required, get_jwt_identity
# from bson import ObjectId, json_util
# import datetime

# ecommerce_bp = Blueprint('ecommerce_bp', __name__)

# @ecommerce_bp.route('/business/my-business', methods=['GET'])
# @jwt_required()
# def get_my_business():
#     """Checks for and returns the current user's business."""
#     db = ecommerce_bp.db
#     businesses_collection = db.businesses
#     current_user_id = ObjectId(get_jwt_identity())
    

#     business = businesses_collection.find_one({"owner_id": current_user_id})
#     print('business', business)

#     if not business:
#         return jsonify({"msg": "No business found for this user"}), 404
        
#     return json.loads(json_util.dumps(business)), 200

# @ecommerce_bp.route('/business/search', methods=['GET'])
# @jwt_required()
# def search_businesses():
#     """Searches for businesses by name or description."""
#     db = ecommerce_bp.db
#     query = request.args.get('q', '')
#     if not query:
#         return jsonify([]), 200

#     businesses = list(db.businesses.find({
#         "$text": {"$search": query}
#     }))
#     return Response(json_util.dumps(businesses), mimetype='application/json'), 200

# @ecommerce_bp.route('/business/owner/<owner_id>', methods=['GET'])
# @jwt_required()
# def get_business_by_owner(owner_id):
#     """Returns the business associated with a specific owner ID."""
#     db = ecommerce_bp.db
#     business = db.businesses.find_one({"owner_id": ObjectId(owner_id)})
#     if not business:
#         return jsonify({"msg": "No business found for this user"}), 404
#     return json.loads(json_util.dumps(business)), 200

# @ecommerce_bp.route('/business/<business_id>/products', methods=['GET'])
# @jwt_required()
# def get_business_products(business_id):
#     """Returns all products for a given business ID."""
#     db = ecommerce_bp.db
#     products = list(db.products.find({"business_id": ObjectId(business_id)}))
#     return Response(json_util.dumps(products), mimetype='application/json'), 200

# @ecommerce_bp.route('/business/<business_id>', methods=['GET'])
# @jwt_required()
# def get_business_details(business_id):
#     """Returns the details for a single business, including owner info."""
#     db = ecommerce_bp.db
#     pipeline = [
#         {'$match': {'_id': ObjectId(business_id)}},
#         {'$lookup': {
#             'from': 'users',
#             'localField': 'owner_id',
#             'foreignField': '_id',
#             'as': 'owner_details'
#         }},
#         {'$unwind': '$owner_details'},
#         {'$project': {'owner_details.password_hash': 0, 'owner_details.email': 0}}
#     ]
#     business = list(db.businesses.aggregate(pipeline))

#     if not business:
#         return jsonify({"msg": "Business not found"}), 404
        
#     return json.loads(json_util.dumps(business[0])), 200

# @ecommerce_bp.route('/business/create', methods=['POST'])
# @jwt_required()
# def create_business():
#     """Creates a new business with a name and images."""
#     db = ecommerce_bp.db
#     businesses_collection = db.businesses
#     current_user_id = ObjectId(get_jwt_identity())
#     data = request.form

#     if 'business_name' not in data:
#         return jsonify({"msg": "Business name is required"}), 400

#     image_paths = []
#     if 'images' in request.files:
#         images = request.files.getlist('images')
#         for image in images:
#             if image.filename != '':
#                 filename = secure_filename(image.filename)
#                 unique_filename = f"{uuid.uuid4()}_{filename}"
#                 upload_folder = 'uploads/business'
#                 if not os.path.exists(upload_folder):
#                     os.makedirs(upload_folder)
                
#                 image_path = os.path.join(upload_folder, unique_filename)
#                 image.save(image_path)
#                 image_paths.append(image_path)

#     new_business = {
#         "owner_id": current_user_id,
#         "business_name": data['business_name'],
#         "description": data.get('description', ''),
#         "images": image_paths,
#         "created_at": datetime.datetime.now(datetime.timezone.utc),
#     }

#     try:
#         businesses_collection.insert_one(new_business)
#         return jsonify({"msg": "Business created successfully"}), 201
#     except Exception as e:
#         return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    
# @ecommerce_bp.route('/product/add', methods=['POST'])
# @jwt_required()
# def add_product():
#     db = ecommerce_bp.db
#     current_user_id = ObjectId(get_jwt_identity())
#     data = request.form

#     required_fields = ['business_id', 'product_name', 'price', 'product_type']
#     if not all(field in data for field in required_fields):
#         return jsonify({"msg": "Missing required fields"}), 400

#     business = db.businesses.find_one({
#         "_id": ObjectId(data['business_id']),
#         "owner_id": current_user_id
#     })
#     if not business:
#         return jsonify({"msg": "Business not found or access denied"}), 404

#     media_paths = []
#     if 'media' in request.files:
#         files = request.files.getlist('media')
#         for file in files:
#             if file.filename != '':
#                 filename = secure_filename(file.filename)
#                 unique_filename = f"{uuid.uuid4()}_{filename}"
#                 upload_folder = 'uploads/products'
#                 if not os.path.exists(upload_folder):
#                     os.makedirs(upload_folder)
                
#                 file_path = os.path.join(upload_folder, unique_filename)
#                 file.save(file_path)
#                 media_paths.append(file_path)

#     new_product = {
#         "business_id": ObjectId(data['business_id']),
#         "product_name": data['product_name'],
#         "description": data.get('description', ''),
#         "price": float(data['price']),
#         "product_type": data['product_type'],
#         "product_sub_type": data.get('product_sub_type'),
#         "media": media_paths,
#         "barcode": data.get('barcode'),
#         "stock_count": int(data.get('stock_count', 1)),
#         "created_at": datetime.datetime.now(datetime.timezone.utc),
#     }

#     try:
#         db.products.insert_one(new_product)
#         return jsonify({"msg": "Product added successfully"}), 201
#     except Exception as e:
#         return jsonify({"msg": "An error occurred", "error": str(e)}), 500

# # NEW: Endpoint for a business owner to update stock quantity
# @ecommerce_bp.route('/product/<product_id>/update-stock', methods=['PUT'])
# @jwt_required()
# def update_product_stock(product_id):
#     """
#     Updates the stock count for a specific product.
#     Only the business owner is authorized to perform this action.
#     """
#     db = ecommerce_bp.db
#     current_user_id = ObjectId(get_jwt_identity())
#     data = request.get_json()
#     new_quantity = data.get('quantity')

#     if new_quantity is None or not isinstance(new_quantity, int) or new_quantity < 0:
#         return jsonify({"msg": "A valid, non-negative integer 'quantity' is required"}), 400

#     try:
#         # 1. Find the product to ensure it exists
#         product = db.products.find_one({"_id": ObjectId(product_id)})
#         if not product:
#             return jsonify({"msg": "Product not found"}), 404

#         # 2. Verify that the current user owns the business this product belongs to
#         business = db.businesses.find_one({
#             "_id": product['business_id'],
#             "owner_id": current_user_id
#         })
#         if not business:
#             return jsonify({"msg": "You are not authorized to update this product"}), 403

#         # 3. If authorized, update the stock count
#         db.products.update_one(
#             {"_id": ObjectId(product_id)},
#             {"$set": {"stock_count": new_quantity}}
#         )
#         return jsonify({"msg": "Stock updated successfully", "new_quantity": new_quantity}), 200

#     except Exception as e:
#         return jsonify({"msg": "An unexpected error occurred", "error": str(e)}), 500

# @ecommerce_bp.route('/delete-product/<product_id>', methods=['DELETE'])
# @jwt_required()
# def delete_product(product_id):
#     db = ecommerce_bp.db
#     products_collection = db.products
#     current_user_id = get_jwt_identity()

#     try:
#         result = products_collection.delete_one({
#             '_id': ObjectId(product_id),
#             'business_id': ObjectId(current_user_id)
#         })
#         if result.deleted_count == 1:
#             return jsonify({"msg": "Product deleted successfully"}), 200
#         else:
#             return jsonify({"msg": "Product not found or not authorized"}), 404
#     except Exception as e:
#         return jsonify({"msg": "An error occurred", "error": str(e)}), 500





import json
from flask import Response, request, jsonify, Blueprint, request
from werkzeug.utils import secure_filename
import os
import uuid
import logging
from flask_jwt_extended import jwt_required, get_jwt_identity
from bson import ObjectId, json_util
import datetime

# Set up logging - only show DEBUG for our app, not third-party libraries
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

# Reduce noise from third-party libraries
logging.getLogger('pymongo').setLevel(logging.WARNING)
logging.getLogger('urllib3').setLevel(logging.WARNING)

ecommerce_bp = Blueprint('ecommerce_bp', __name__)

@ecommerce_bp.route('/business/my-business', methods=['GET'])
@jwt_required()
def get_my_business():
    """Checks for and returns the current user's business."""
    db = ecommerce_bp.db
    businesses_collection = db.businesses
    current_user_id = ObjectId(get_jwt_identity())
    business = businesses_collection.find_one({"owner_id": current_user_id})
    
    if not business:
        return jsonify({"msg": "No business found for this user"}), 404
        
    return json.loads(json_util.dumps(business)), 200

@ecommerce_bp.route('/business/search', methods=['GET'])
@jwt_required()
def search_businesses():
    """Searches for businesses by name or description."""
    db = ecommerce_bp.db
    query = request.args.get('q', '')
    if not query:
        return jsonify([]), 200

    businesses = list(db.businesses.find({
        "$text": {"$search": query}
    }))
    return Response(json_util.dumps(businesses), mimetype='application/json'), 200

@ecommerce_bp.route('/business/owner/<owner_id>', methods=['GET'])
@jwt_required()
def get_business_by_owner(owner_id):
    """Returns the business associated with a specific owner ID."""
    db = ecommerce_bp.db
    business = db.businesses.find_one({"owner_id": ObjectId(owner_id)})
    if not business:
        return jsonify({"msg": "No business found for this user"}), 404
    return json.loads(json_util.dumps(business)), 200

@ecommerce_bp.route('/business/<business_id>/products', methods=['GET'])
@jwt_required()
def get_business_products(business_id):
    """Returns all products for a given business ID."""
    db = ecommerce_bp.db
    products = list(db.products.find({"business_id": ObjectId(business_id)}))
    return Response(json_util.dumps(products), mimetype='application/json'), 200

@ecommerce_bp.route('/business/<business_id>', methods=['GET'])
@jwt_required()
def get_business_details(business_id):
    """Returns the details for a single business, including owner info."""
    db = ecommerce_bp.db
    pipeline = [
        {'$match': {'_id': ObjectId(business_id)}},
        {'$lookup': {
            'from': 'users',
            'localField': 'owner_id',
            'foreignField': '_id',
            'as': 'owner_details'
        }},
        {'$unwind': '$owner_details'},
        {'$project': {'owner_details.password_hash': 0, 'owner_details.email': 0}}
    ]
    business = list(db.businesses.aggregate(pipeline))

    if not business:
        return jsonify({"msg": "Business not found"}), 404
        
    return json.loads(json_util.dumps(business[0])), 200

@ecommerce_bp.route('/business/create', methods=['POST'])
@jwt_required()
def create_business():
    """Creates a new business with a name and images."""
    db = ecommerce_bp.db
    businesses_collection = db.businesses
    current_user_id = ObjectId(get_jwt_identity())
    data = request.form

    if 'business_name' not in data:
        return jsonify({"msg": "Business name is required"}), 400

    image_paths = []
    if 'images' in request.files:
        images = request.files.getlist('images')
        for image in images:
            if image.filename != '':
                filename = secure_filename(image.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads/business'
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                image_path = os.path.join(upload_folder, unique_filename)
                image.save(image_path)
                image_paths.append(image_path)

    new_business = {
        "owner_id": current_user_id,
        "business_name": data['business_name'],
        "description": data.get('description', ''),
        "images": image_paths,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
    }

    try:
        businesses_collection.insert_one(new_business)
        return jsonify({"msg": "Business created successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    


@ecommerce_bp.route('/product/add', methods=['POST'])
@jwt_required()
def add_product():
    db = ecommerce_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.form

    required_fields = ['business_id', 'product_name', 'price', 'product_type']
    if not all(field in data for field in required_fields):
        return jsonify({"msg": "Missing required fields"}), 400

    business = db.businesses.find_one({
        "_id": ObjectId(data['business_id']),
        "owner_id": current_user_id
    })
    if not business:
        return jsonify({"msg": "Business not found or access denied"}), 404

    media_paths = []
    if 'media' in request.files:
        files = request.files.getlist('media')
        for file in files:
            if file.filename != '':
                filename = secure_filename(file.filename)
                unique_filename = f"{uuid.uuid4()}_{filename}"
                upload_folder = 'uploads/products'
                if not os.path.exists(upload_folder):
                    os.makedirs(upload_folder)
                
                file_path = os.path.join(upload_folder, unique_filename)
                file.save(file_path)
                media_paths.append(file_path)

    stock_count_str = data.get('stock_count', '1')
    stock_count = int(stock_count_str) if stock_count_str.isdigit() else 0
    product_type = data.get('product_type')

    new_product = {
        "business_id": ObjectId(data['business_id']),
        "product_name": data['product_name'],
        "description": data.get('description', ''),
        "price": float(data['price']),
        "product_type": product_type,
        "product_sub_type": data.get('product_sub_type'),
        "media": media_paths,
        "barcode": data.get('barcode'),
        "stock_count": stock_count,
        "created_at": datetime.datetime.now(datetime.timezone.utc),
    }

    # --- REFACTORED: Only add duration for 'service' products ---
    if product_type == 'service':
        new_product['duration_minutes'] = int(data.get('duration_minutes', 60))
    # --- End of REFACTOR ---

    try:
        db.products.insert_one(new_product)
        return jsonify({"msg": "Product added successfully"}), 201
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500

# NEW: Endpoint for a business owner to update stock quantity
@ecommerce_bp.route('/product/<product_id>/update-stock', methods=['PUT'])
@jwt_required()
def update_product_stock(product_id):
    """
    Updates the stock count for a specific product.
    Only the business owner is authorized to perform this action.
    """
    db = ecommerce_bp.db
    current_user_id = ObjectId(get_jwt_identity())
    data = request.get_json()
    new_quantity = data.get('quantity')

    if new_quantity is None or not isinstance(new_quantity, int) or new_quantity < 0:
        return jsonify({"msg": "A valid, non-negative integer 'quantity' is required"}), 400

    try:
        product = db.products.find_one({"_id": ObjectId(product_id)})
        if not product:
            return jsonify({"msg": "Product not found"}), 404

        business = db.businesses.find_one({
            "_id": product['business_id'],
            "owner_id": current_user_id
        })
        if not business:
            return jsonify({"msg": "You are not authorized to update this product"}), 403

        db.products.update_one(
            {"_id": ObjectId(product_id)},
            {"$set": {"stock_count": new_quantity}}
        )
        return jsonify({"msg": "Stock updated successfully", "new_quantity": new_quantity}), 200

    except Exception as e:
        return jsonify({"msg": "An unexpected error occurred", "error": str(e)}), 500
    

# Add this new route to your ecommerce.py file, for example, after the update_product_stock function.

@ecommerce_bp.route('/product/<product_id>/add-stock', methods=['PUT'])
@jwt_required()
def add_product_stock(product_id):
    """
    Increments the stock count for a specific product by a given amount.
    Only the business owner is authorized to perform this action.
    """
    try:
        logger.debug("=== Starting add_product_stock endpoint ===")
        logger.debug(f"Product ID: {product_id}")

        db = ecommerce_bp.db
        current_user_id = ObjectId(get_jwt_identity())
        logger.debug(f"Current user ID: {current_user_id}")

        data = request.get_json()
        logger.debug(f"Request data: {data}")

        quantity_to_add = data.get('quantity')
        logger.debug(f"Quantity to add: {quantity_to_add}")

        if quantity_to_add is None or not isinstance(quantity_to_add, int) or quantity_to_add < 0:
            logger.error(f"Invalid quantity: {quantity_to_add}")
            return jsonify({"msg": "A valid, non-negative integer 'quantity' is required"}), 400

        logger.debug("Looking up product...")
        product = db.products.find_one({"_id": ObjectId(product_id)})
        if not product:
            logger.error(f"Product not found: {product_id}")
            return jsonify({"msg": "Product not found"}), 404

        logger.debug(f"Product found: {product.get('product_name', 'Unknown')}")
        logger.debug(f"Current stock: {product.get('stock_count', 0)}")

        logger.debug("Checking business authorization...")
        business = db.businesses.find_one({
            "_id": product['business_id'],
            "owner_id": current_user_id
        })
        if not business:
            logger.error(f"Authorization failed for user {current_user_id} and business {product['business_id']}")
            return jsonify({"msg": "You are not authorized to update this product"}), 403

        logger.debug(f"Authorization successful for business: {business['_id']}")

        # Use the $inc operator to atomically add to the stock count
        logger.debug(f"Updating stock: adding {quantity_to_add} to current stock")
        result = db.products.update_one(
            {"_id": ObjectId(product_id)},
            {"$inc": {"stock_count": quantity_to_add}}
        )

        if result.matched_count == 0:
            logger.error("Product not found during update")
            return jsonify({"msg": "Product not found during update"}), 404

        logger.debug(f"Stock update successful. Modified count: {result.modified_count}")
        return jsonify({"msg": "Stock added successfully"}), 200

    except Exception as e:
        logger.error(f"Error in add_product_stock: {e}")
        return jsonify({"msg": "An unexpected error occurred", "error": str(e)}), 500

@ecommerce_bp.route('/delete-product/<product_id>', methods=['DELETE'])
@jwt_required()
def delete_product(product_id):
    db = ecommerce_bp.db
    products_collection = db.products
    current_user_id = get_jwt_identity()

    try:
        result = products_collection.delete_one({
            '_id': ObjectId(product_id),
            'business_id': ObjectId(current_user_id)
        })
        if result.deleted_count == 1:
            return jsonify({"msg": "Product deleted successfully"}), 200
        else:
            return jsonify({"msg": "Product not found or not authorized"}), 404
    except Exception as e:
        return jsonify({"msg": "An error occurred", "error": str(e)}), 500
    

# Add this new route to your ecommerce.py file

@ecommerce_bp.route('/business/my-businesses', methods=['GET'])
@jwt_required()
def get_my_businesses():
    """Returns a list of all businesses owned by the current user."""
    db = ecommerce_bp.db
    businesses_collection = db.businesses
    current_user_id = ObjectId(get_jwt_identity())
    
    # Find all businesses where the owner_id matches the current user
    user_businesses = list(businesses_collection.find({"owner_id": current_user_id}))

    return Response(json_util.dumps(user_businesses), mimetype='application/json'), 200